"""add sw versions

Revision ID: 11f9ba6db041
Revises: 8c281a2e6102
Create Date: 2025-04-15 17:42:35.687915

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '11f9ba6db041'
down_revision: Union[str, None] = '8c281a2e6102'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('tests', sa.Column('autopilot_software_version', sa.String(length=100), nullable=True))
    op.add_column('tests', sa.Column('jetson_software_version', sa.String(length=100), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('tests', 'jetson_software_version')
    op.drop_column('tests', 'autopilot_software_version')
    # ### end Alembic commands ###
