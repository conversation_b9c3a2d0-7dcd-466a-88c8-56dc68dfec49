from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql


# revision identifiers, used by Alembic.
revision = '2135f4b3cf89'
down_revision = '5382331529b4'
branch_labels = None
depends_on = None


# Enum definitions
environment_enum = postgresql.ENUM('SIM', 'HIL', 'IRL', name='environmentenum')
vtol_mode_enum = postgresql.ENUM(
    'MC_MODE', 'DURING_FRONT_TRANSITION', 'FW_MODE', 'DURING_BACK_TRANSITION',
    'MISSION_INHERITED', 'ON_GROUND', name='vtolmodeenum')
priority_level_enum = postgresql.ENUM('LOW', 'MEDIUM', 'HIGH', name='prioritylevelenum')
sim_world_enum = postgresql.ENUM('WINDY', 'CANYON', 'PIVOT', name='simworldenum')
wind_mode_enum = postgresql.ENUM(
    'NONE', 'SIDE_WIND', 'TAIL_WIND', 'HEAD_WIND', 'VERTICAL_WIND', 'ENVIRONMENT',
    name='windmodeenum')
autopilot_mode_enum = postgresql.ENUM(
    'MANUAL', 'STABILIZED', 'ACRO', 'ALTITUDE', 'POSITION', 'HOLD', 'MISSION',
    'RALLY_POINT_LAND', 'LAND', 'PRECISION_LAND', 'TAKEOFF', 'DISARMED', 'ARMED',
    name='autopilotmodeenum')

test_type_enum = postgresql.ENUM('DEFAULT', 'NORMAL', 'CONTINGENCY', 'EMERGENCY', name='testtypeenum')


def upgrade() -> None:
    # Create enum types explicitly
    environment_enum.create(op.get_bind(), checkfirst=True)
    vtol_mode_enum.create(op.get_bind(), checkfirst=True)
    priority_level_enum.create(op.get_bind(), checkfirst=True)
    sim_world_enum.create(op.get_bind(), checkfirst=True)
    wind_mode_enum.create(op.get_bind(), checkfirst=True)
    autopilot_mode_enum.create(op.get_bind(), checkfirst=True)
    test_type_enum.create(op.get_bind(), checkfirst=True)

    # Create 'group' table
    op.create_table(
        'group',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(length=100), nullable=False),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('name')
    )
    op.create_index(op.f('ix_group_id'), 'group', ['id'], unique=False)

    # Add new columns to 'test_procedures' and modify existing ones
    op.add_column('test_procedures', sa.Column('environment', environment_enum, nullable=False))
    op.add_column('test_procedures', sa.Column('group_id', sa.Integer(), nullable=False))
    op.add_column('test_procedures', sa.Column('vtol_mode', vtol_mode_enum, nullable=True))

    # test_type column
    op.drop_column('test_procedures', 'test_type')
    op.add_column('test_procedures', sa.Column(
        'test_type', sa.Enum('DEFAULT', 'NORMAL', 'CONTINGENCY', 'EMERGENCY', name='testtypeenum'),
        nullable=False))

    # priority_level column
    op.drop_column('test_procedures', 'priority_level')
    op.add_column('test_procedures', sa.Column(
        'priority_level', priority_level_enum, nullable=True))

    # sim_world column
    op.drop_column('test_procedures', 'sim_world')
    op.add_column('test_procedures', sa.Column(
        'sim_world', sim_world_enum, nullable=True))

    # wind_mode column
    op.drop_column('test_procedures', 'wind_mode')
    op.add_column('test_procedures', sa.Column(
        'wind_mode', wind_mode_enum, nullable=True))

    # autopilot_mode column
    op.drop_column('test_procedures', 'autopilot_mode')
    op.add_column('test_procedures', sa.Column(
        'autopilot_mode', autopilot_mode_enum, nullable=True))

    # Add foreign key constraint for 'group_id'
    op.create_foreign_key('fk_test_procedures_group_id', 'test_procedures', 'group', ['group_id'], ['id'])

    # Drop old columns that are no longer needed
    op.drop_column('test_procedures', 'group')
    op.drop_column('test_procedures', 'flight_mode')

    # Add simulation column to 'tests'
    op.add_column('tests', sa.Column('simulation', sa.Boolean(), nullable=True))


def downgrade() -> None:
    # Drop added columns and foreign keys
    op.drop_column('tests', 'simulation')
    op.add_column('test_procedures', sa.Column('flight_mode', sa.VARCHAR(length=100), autoincrement=False, nullable=True))
    op.add_column('test_procedures', sa.Column('group', sa.VARCHAR(length=100), autoincrement=False, nullable=True))

    op.drop_constraint('fk_test_procedures_group_id', 'test_procedures', type_='foreignkey')

    # Revert column alterations
    # autopilot_mode column
    op.drop_column('test_procedures', 'autopilot_mode')
    op.add_column('test_procedures', sa.Column(
        'autopilot_mode', sa.VARCHAR(length=100), nullable=True))

    # wind_mode column
    op.drop_column('test_procedures', 'wind_mode')
    op.add_column('test_procedures', sa.Column(
        'wind_mode', sa.VARCHAR(length=20), nullable=True))

    # sim_world column
    op.drop_column('test_procedures', 'sim_world')
    op.add_column('test_procedures', sa.Column(
        'sim_world', sa.VARCHAR(length=100), nullable=True))

    # priority_level column
    op.drop_column('test_procedures', 'priority_level')
    op.add_column('test_procedures', sa.Column(
        'priority_level', sa.VARCHAR(length=100), nullable=True))

    # test_type column
    op.drop_column('test_procedures', 'test_type')
    op.add_column('test_procedures', sa.Column(
        'test_type', sa.VARCHAR(length=100), nullable=True))

    op.drop_column('test_procedures', 'vtol_mode')
    op.drop_column('test_procedures', 'group_id')
    op.drop_column('test_procedures', 'environment')

    # Drop 'group' table
    op.drop_index(op.f('ix_group_id'), table_name='group')
    op.drop_table('group')

    # Drop enum types explicitly
    autopilot_mode_enum.drop(op.get_bind(), checkfirst=True)
    wind_mode_enum.drop(op.get_bind(), checkfirst=True)
    sim_world_enum.drop(op.get_bind(), checkfirst=True)
    priority_level_enum.drop(op.get_bind(), checkfirst=True)
    vtol_mode_enum.drop(op.get_bind(), checkfirst=True)
    environment_enum.drop(op.get_bind(), checkfirst=True)
    test_type_enum.drop(op.get_bind(), checkfirst=True)
