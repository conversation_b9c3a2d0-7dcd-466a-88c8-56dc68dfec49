"""add obsolete flag to test procedures

Revision ID: 222c1d57dfff
Revises: a089e9ec7770
Create Date: 2024-11-25 15:32:41.823592

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '222c1d57dfff'
down_revision: Union[str, None] = 'a089e9ec7770'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('test_procedures', sa.Column('obsolete', sa.<PERSON>(), server_default='false', nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('test_procedures', 'obsolete')
    # ### end Alembic commands ###
