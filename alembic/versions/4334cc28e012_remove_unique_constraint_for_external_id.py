"""remove unique constraint for external ID

Revision ID: 4334cc28e012
Revises: 222c1d57dfff
Create Date: 2024-11-25 16:35:05.681438

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '4334cc28e012'
down_revision: Union[str, None] = '222c1d57dfff'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('ix_test_procedures_external_id', table_name='test_procedures')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index('ix_test_procedures_external_id', 'test_procedures', ['external_id'], unique=True)
    # ### end Alembic commands ###
