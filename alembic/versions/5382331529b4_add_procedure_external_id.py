"""Add procedure external id

Revision ID: 5382331529b4
Revises: b8819a4d9b4f
Create Date: 2024-07-30 17:49:18.626584

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '5382331529b4'
down_revision: Union[str, None] = 'b8819a4d9b4f'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('test_procedures', sa.Column('external_id', sa.Integer(), nullable=False))
    op.create_index(op.f('ix_test_procedures_external_id'), 'test_procedures', ['external_id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_test_procedures_external_id'), table_name='test_procedures')
    op.drop_column('test_procedures', 'external_id')
    # ### end Alembic commands ###
