"""Remove array cols

Revision ID: 564d0f96b96a
Revises: e498178a6f10
Create Date: 2024-09-16 22:38:34.716990

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '564d0f96b96a'
down_revision: Union[str, None] = 'e498178a6f10'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('test_procedures', 'expectations_field')
    op.drop_column('test_procedures', 'procedures_field')
    op.drop_column('test_procedures', 'procedures_simulation')
    op.drop_column('test_procedures', 'expectations_hil')
    op.drop_column('test_procedures', 'procedures_hil')
    op.drop_column('test_procedures', 'expectations_simulation')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('test_procedures', sa.Column('expectations_simulation', sa.TEXT(), autoincrement=False, nullable=True))
    op.add_column('test_procedures', sa.Column('procedures_hil', sa.TEXT(), autoincrement=False, nullable=True))
    op.add_column('test_procedures', sa.Column('expectations_hil', sa.TEXT(), autoincrement=False, nullable=True))
    op.add_column('test_procedures', sa.Column('procedures_simulation', sa.TEXT(), autoincrement=False, nullable=True))
    op.add_column('test_procedures', sa.Column('procedures_field', sa.TEXT(), autoincrement=False, nullable=True))
    op.add_column('test_procedures', sa.Column('expectations_field', sa.TEXT(), autoincrement=False, nullable=True))
    # ### end Alembic commands ###
