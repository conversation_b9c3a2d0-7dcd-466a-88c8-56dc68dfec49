"""empty message

Revision ID: 65d201d20433
Revises: caf07f349835
Create Date: 2025-07-25 15:27:35.239839

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '65d201d20433'
down_revision: Union[str, None] = 'caf07f349835'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('test_procedures', sa.Column('is_default', sa.<PERSON>(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('test_procedures', 'is_default')
    # ### end Alembic commands ###
