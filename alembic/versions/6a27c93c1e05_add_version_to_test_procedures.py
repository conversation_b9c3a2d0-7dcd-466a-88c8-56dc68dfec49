"""add version to test procedures

Revision ID: 6a27c93c1e05
Revises: f89821265c4a
Create Date: 2024-11-14 13:12:05.479938

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '6a27c93c1e05'
down_revision: Union[str, None] = 'f89821265c4a'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('test_procedures', sa.Column('version', sa.Integer(), server_default='1', nullable=False))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('test_procedures', 'version')
    # ### end Alembic commands ###
