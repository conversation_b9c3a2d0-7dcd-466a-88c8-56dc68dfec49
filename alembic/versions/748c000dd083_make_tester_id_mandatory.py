"""Make tester ID mandatory

Revision ID: 748c000dd083
Revises: cc94aa30e68c
Create Date: 2024-09-18 17:13:19.049462

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '748c000dd083'
down_revision: Union[str, None] = 'cc94aa30e68c'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('tests', 'tester_id',
               existing_type=sa.UUID(),
               nullable=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('tests', 'tester_id',
               existing_type=sa.UUID(),
               nullable=True)
    # ### end Alembic commands ###
