"""Initial tables

Revision ID: 8179b9b9085b
Revises: 
Create Date: 2024-07-30 15:12:40.989376

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '8179b9b9085b'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('test_procedures',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=100), nullable=True),
    sa.Column('test_type', sa.String(length=100), nullable=True),
    sa.Column('group', sa.String(length=100), nullable=True),
    sa.Column('priority_level', sa.String(length=100), nullable=True),
    sa.Column('simulation', sa.<PERSON>(), nullable=True),
    sa.Column('field_test', sa.<PERSON>(), nullable=True),
    sa.Column('hil_test', sa.<PERSON>olean(), nullable=True),
    sa.Column('qgroundcontrol', sa.Boolean(), nullable=True),
    sa.Column('precland_docker', sa.Boolean(), nullable=True),
    sa.Column('mdp', sa.Boolean(), nullable=True),
    sa.Column('sim_world', sa.String(length=100), nullable=True),
    sa.Column('autopilot_mode', sa.String(length=100), nullable=True),
    sa.Column('flight_mode', sa.String(length=100), nullable=True),
    sa.Column('mission_plan', sa.String(length=60), nullable=True),
    sa.Column('wind_mode', sa.String(length=20), nullable=True),
    sa.Column('wind_speed', sa.Integer(), nullable=True),
    sa.Column('procedures_simulation', sa.Text(), nullable=True),
    sa.Column('expectations_simulation', sa.Text(), nullable=True),
    sa.Column('procedures_field', sa.Text(), nullable=True),
    sa.Column('expectations_field', sa.Text(), nullable=True),
    sa.Column('procedures_hil', sa.Text(), nullable=True),
    sa.Column('expectations_hil', sa.Text(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_test_procedures_id'), 'test_procedures', ['id'], unique=False)
    op.create_index(op.f('ix_test_procedures_name'), 'test_procedures', ['name'], unique=False)
    op.create_table('tests',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('test_procedure_id', sa.Integer(), nullable=True),
    sa.Column('date', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('tester_id', sa.UUID(), nullable=True),
    sa.Column('glider', sa.String(length=100), nullable=True),
    sa.Column('success_status', sa.Boolean(), nullable=True),
    sa.Column('notes', sa.Text(), nullable=True),
    sa.Column('flight_review_log_url', sa.String(length=250), nullable=True),
    sa.Column('automated_execution_status', sa.String(length=100), nullable=True),
    sa.Column('celery_task_id', sa.String(length=100), nullable=True),
    sa.ForeignKeyConstraint(['test_procedure_id'], ['test_procedures.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_tests_id'), 'tests', ['id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_tests_id'), table_name='tests')
    op.drop_table('tests')
    op.drop_index(op.f('ix_test_procedures_name'), table_name='test_procedures')
    op.drop_index(op.f('ix_test_procedures_id'), table_name='test_procedures')
    op.drop_table('test_procedures')
    # ### end Alembic commands ###
