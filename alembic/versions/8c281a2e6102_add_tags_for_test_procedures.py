"""add tags for test procedures

Revision ID: 8c281a2e6102
Revises: 4334cc28e012
Create Date: 2024-12-03 15:11:43.974491

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '8c281a2e6102'
down_revision: Union[str, None] = '4334cc28e012'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('test_procedure_tag_lookup',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('display_name', sa.String(length=100), nullable=False),
    sa.Column('description', sa.String(length=250), nullable=True),
    sa.Column('is_default', sa.<PERSON>(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_test_procedure_tag_lookup_id'), 'test_procedure_tag_lookup', ['id'], unique=False)
    op.create_table('test_procedure_tag_map',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('test_procedure_id', sa.Integer(), nullable=True),
    sa.Column('tag_id', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['tag_id'], ['test_procedure_tag_lookup.id'], ),
    sa.ForeignKeyConstraint(['test_procedure_id'], ['test_procedures.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_test_procedure_tag_map_id'), 'test_procedure_tag_map', ['id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_test_procedure_tag_map_id'), table_name='test_procedure_tag_map')
    op.drop_table('test_procedure_tag_map')
    op.drop_index(op.f('ix_test_procedure_tag_lookup_id'), table_name='test_procedure_tag_lookup')
    op.drop_table('test_procedure_tag_lookup')
    # ### end Alembic commands ###
