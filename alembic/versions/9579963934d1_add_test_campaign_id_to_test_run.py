"""add Test campaign ID to Test Run

Revision ID: 9579963934d1
Revises: 11f9ba6db041
Create Date: 2025-07-24 11:20:36.304260

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '9579963934d1'
down_revision: Union[str, None] = '11f9ba6db041'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('tests', sa.Column('test_campaign_id', sa.Integer(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('tests', 'test_campaign_id')
    # ### end Alembic commands ###
