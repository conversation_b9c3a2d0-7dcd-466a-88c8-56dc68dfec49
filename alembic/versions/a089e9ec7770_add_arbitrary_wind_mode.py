"""add arbitrary wind mode

Revision ID: a089e9ec7770
Revises: 6a27c93c1e05
Create Date: 2024-11-15 12:34:30.273656

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'a089e9ec7770'
down_revision: Union[str, None] = '6a27c93c1e05'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Add a new value to the environmentenum enum
    op.execute("ALTER TYPE windmodeenum ADD VALUE 'ARBITRARY'")


def downgrade() -> None:
    # Downgrading enums is not straightforward. PostgreSQL does not support removing enum values directly.
    # A possible workaround is to create a new enum type without the value, alter the columns to use the new type, and drop the old type.
    op.execute("""
    DO $$
    BEGIN
        -- Create a new enum type without the 'ARBITRARY' value
        CREATE TYPE windmodeenum_new AS ENUM(
            'NONE', 'SIDE_WIND', 'TAIL_WIND', 'HEAD_WIND', 'VERTICAL_WIND', 'ENVIRONMENT'
        );

        -- Alter the wind_mode column in the test_procedures table to use the new type
        ALTER TABLE test_procedures ALTER COLUMN wind_mode TYPE windmodeenum_new 
        USING wind_mode::text::windmodeenum_new;

        -- Drop the old enum type
        DROP TYPE windmodeenum;

        -- Rename the new enum type to the original name
        ALTER TYPE windmodeenum_new RENAME TO windmodeenum;
    END $$;
    """)
