"""Add automated flags

Revision ID: b8819a4d9b4f
Revises: 8179b9b9085b
Create Date: 2024-07-30 17:40:07.401013

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'b8819a4d9b4f'
down_revision: Union[str, None] = '8179b9b9085b'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('test_procedures', sa.Column('automated', sa.Bo<PERSON>an(), nullable=True))
    op.add_column('tests', sa.Column('automated', sa.Boolean(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('tests', 'automated')
    op.drop_column('test_procedures', 'automated')
    # ### end Alembic commands ###
