"""add comprehensive sw component tracking

Revision ID: caf07f349835
Revises: fd1a8dd55964
Create Date: 2025-07-24 12:57:42.128351

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'caf07f349835'
down_revision: Union[str, None] = 'fd1a8dd55964'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Add new SW component columns with default values
    op.add_column('tests', sa.Column('r_autopilot_version', sa.String(length=100), nullable=True, server_default='not_tracked'))
    op.add_column('tests', sa.Column('r_autopilot_rc_number', sa.Integer(), nullable=True, server_default='0'))
    op.add_column('tests', sa.Column('glider_companion_version', sa.String(length=100), nullable=True, server_default='not_tracked'))
    op.add_column('tests', sa.Column('glider_companion_rc_number', sa.Integer(), nullable=True, server_default='0'))
    op.add_column('tests', sa.Column('qgroundcontrol_version', sa.String(length=100), nullable=True, server_default='not_tracked'))
    op.add_column('tests', sa.Column('qgroundcontrol_rc_number', sa.Integer(), nullable=True, server_default='0'))
    op.add_column('tests', sa.Column('reference_parameters_version', sa.String(length=100), nullable=True, server_default='not_tracked'))
    op.add_column('tests', sa.Column('reference_parameters_rc_number', sa.Integer(), nullable=True, server_default='0'))
    op.add_column('tests', sa.Column('force_sensor_version', sa.String(length=100), nullable=True, server_default='not_tracked'))
    op.add_column('tests', sa.Column('force_sensor_rc_number', sa.Integer(), nullable=True, server_default='0'))
    op.add_column('tests', sa.Column('landing_station_mercury_version', sa.String(length=100), nullable=True, server_default='not_tracked'))
    op.add_column('tests', sa.Column('landing_station_mercury_rc_number', sa.Integer(), nullable=True, server_default='0'))
    op.add_column('tests', sa.Column('r_autopilot_fts_version', sa.String(length=100), nullable=True, server_default='not_tracked'))
    op.add_column('tests', sa.Column('r_autopilot_fts_rc_number', sa.Integer(), nullable=True, server_default='0'))
    op.add_column('tests', sa.Column('fts_comms_server_version', sa.String(length=100), nullable=True, server_default='not_tracked'))
    op.add_column('tests', sa.Column('fts_comms_server_rc_number', sa.Integer(), nullable=True, server_default='0'))
    op.add_column('tests', sa.Column('fts_trigger_android_app_version', sa.String(length=100), nullable=True, server_default='not_tracked'))
    op.add_column('tests', sa.Column('fts_trigger_android_app_rc_number', sa.Integer(), nullable=True, server_default='0'))

    # Migrate existing data from old fields to new fields
    # Migrate autopilot_software_version to r_autopilot_version
    op.execute("""
        UPDATE tests
        SET r_autopilot_version = COALESCE(autopilot_software_version, 'not_tracked'),
            r_autopilot_rc_number = 0
        WHERE autopilot_software_version IS NOT NULL
    """)

    # Migrate jetson_software_version to glider_companion_version
    op.execute("""
        UPDATE tests
        SET glider_companion_version = COALESCE(jetson_software_version, 'not_tracked'),
            glider_companion_rc_number = 0
        WHERE jetson_software_version IS NOT NULL
    """)

    # Drop old columns
    op.drop_column('tests', 'autopilot_software_version')
    op.drop_column('tests', 'jetson_software_version')


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('tests', sa.Column('jetson_software_version', sa.VARCHAR(length=100), autoincrement=False, nullable=True))
    op.add_column('tests', sa.Column('autopilot_software_version', sa.VARCHAR(length=100), autoincrement=False, nullable=True))
    op.drop_column('tests', 'fts_trigger_android_app_rc_number')
    op.drop_column('tests', 'fts_trigger_android_app_version')
    op.drop_column('tests', 'fts_comms_server_rc_number')
    op.drop_column('tests', 'fts_comms_server_version')
    op.drop_column('tests', 'r_autopilot_fts_rc_number')
    op.drop_column('tests', 'r_autopilot_fts_version')
    op.drop_column('tests', 'landing_station_mercury_rc_number')
    op.drop_column('tests', 'landing_station_mercury_version')
    op.drop_column('tests', 'force_sensor_rc_number')
    op.drop_column('tests', 'force_sensor_version')
    op.drop_column('tests', 'reference_parameters_rc_number')
    op.drop_column('tests', 'reference_parameters_version')
    op.drop_column('tests', 'qgroundcontrol_rc_number')
    op.drop_column('tests', 'qgroundcontrol_version')
    op.drop_column('tests', 'glider_companion_rc_number')
    op.drop_column('tests', 'glider_companion_version')
    op.drop_column('tests', 'r_autopilot_rc_number')
    op.drop_column('tests', 'r_autopilot_version')
    # ### end Alembic commands ###
