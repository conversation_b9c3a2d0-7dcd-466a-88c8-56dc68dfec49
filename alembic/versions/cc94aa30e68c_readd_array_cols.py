"""Readd array cols

Revision ID: cc94aa30e68c
Revises: 564d0f96b96a
Create Date: 2024-09-16 22:38:54.952018

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'cc94aa30e68c'
down_revision: Union[str, None] = '564d0f96b96a'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('test_procedures', sa.Column('procedures_simulation', postgresql.ARRAY(sa.Text()), nullable=True))
    op.add_column('test_procedures', sa.Column('expectations_simulation', postgresql.ARRAY(sa.Text()), nullable=True))
    op.add_column('test_procedures', sa.Column('procedures_field', postgresql.ARRAY(sa.Text()), nullable=True))
    op.add_column('test_procedures', sa.Column('expectations_field', postgresql.ARRAY(sa.Text()), nullable=True))
    op.add_column('test_procedures', sa.Column('procedures_hil', postgresql.ARRAY(sa.Text()), nullable=True))
    op.add_column('test_procedures', sa.Column('expectations_hil', postgresql.ARRAY(sa.Text()), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('test_procedures', 'expectations_hil')
    op.drop_column('test_procedures', 'procedures_hil')
    op.drop_column('test_procedures', 'expectations_field')
    op.drop_column('test_procedures', 'procedures_field')
    op.drop_column('test_procedures', 'expectations_simulation')
    op.drop_column('test_procedures', 'procedures_simulation')
    # ### end Alembic commands ###
