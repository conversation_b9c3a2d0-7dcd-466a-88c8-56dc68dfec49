"""Remove env col

Revision ID: e498178a6f10
Revises: 2135f4b3cf89
Create Date: 2024-09-13 16:28:42.795641

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'e498178a6f10'
down_revision: Union[str, None] = '2135f4b3cf89'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('test_procedures', 'environment')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('test_procedures', sa.Column('environment', postgresql.ENUM('SIM', 'HIL', 'IRL', name='environmentenum'), autoincrement=False, nullable=False))
    # ### end Alembic commands ###
