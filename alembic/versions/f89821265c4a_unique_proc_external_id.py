"""unique proc external ID

Revision ID: f89821265c4a
Revises: 748c000dd083
Create Date: 2024-09-19 13:37:38.545561

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'f89821265c4a'
down_revision: Union[str, None] = '748c000dd083'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('ix_test_procedures_external_id', table_name='test_procedures')
    op.create_index(op.f('ix_test_procedures_external_id'), 'test_procedures', ['external_id'], unique=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_test_procedures_external_id'), table_name='test_procedures')
    op.create_index('ix_test_procedures_external_id', 'test_procedures', ['external_id'], unique=False)
    # ### end Alembic commands ###
