"""add manually cleared flag for ignored Test Runs

Revision ID: fd1a8dd55964
Revises: 9579963934d1
Create Date: 2025-07-24 11:32:59.582251

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'fd1a8dd55964'
down_revision: Union[str, None] = '9579963934d1'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('tests', sa.Column('manually_cleared', sa.<PERSON>(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('tests', 'manually_cleared')
    # ### end Alembic commands ###
