from celery import Celery
from app.config import settings

rabbitmq_host = settings.rabbitmq.host
port = settings.rabbitmq.port
user = settings.rabbitmq.username
password = settings.rabbitmq.password

rabbitmq_url = f"amqp://{user}:{password}@{rabbitmq_host}:{port}"
backend_url = f"rpc://{user}:{password}@{rabbitmq_host}:{port}"
app = Celery('flight-tests', broker=rabbitmq_url, backend=backend_url, task_default_queue='flight_tests_tasks',
    task_queues={
        'flight_tests_tasks': {'exchange': 'flight_tests_tasks', 'routing_key': 'flight_tests.#'},
    })

start_sim_test = app.signature('flight_tests_runner.run_test')

