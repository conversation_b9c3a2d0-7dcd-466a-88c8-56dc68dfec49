from pathlib import Path
from pydantic_settings import BaseSettings
from omegaconf import OmegaConf

from dotenv import load_dotenv

load_dotenv()

config_path = Path(__file__).parent.parent / "config.yaml"
raw_conf = OmegaConf.load(config_path)

config_dict = OmegaConf.to_container(raw_conf, resolve=True)


class DatabaseSettings(BaseSettings):
    url: str

class KeycloakSettings(BaseSettings):
    url: str
    realm: str
    client_id: str
    client_secret: str

class RabbitMQSettings(BaseSettings):
    host: str
    port: int
    username: str
    password: str

class Microservices(BaseSettings):
    glider: str

class AppConfig(BaseSettings):
    database: DatabaseSettings = DatabaseSettings(**config_dict["database"])
    keycloak: KeycloakSettings = KeycloakSettings(**config_dict['keycloak'])
    rabbitmq: RabbitMQSettings = RabbitMQSettings(**config_dict["rabbitmq"])
    microservices: Microservices = Microservices(**config_dict["microservices"])

settings = AppConfig()
