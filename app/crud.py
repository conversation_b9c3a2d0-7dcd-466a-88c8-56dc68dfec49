from sqlalchemy.orm import Session
from app import models, schemas
from app.celery import start_sim_test, app as celery_app
import logging
from app.models import TestTypeEnum, EnvironmentEnum, PriorityLevelEnum, SimWorldEnum, WindModeEnum, AutopilotModeEnum, VtolModeEnum
import httpx
import csv
from io import StringIO
from app.config import settings
from sqlalchemy.exc import IntegrityError, NoResultFound
from fastapi import HTTPException
from datetime import datetime
from sqlalchemy.sql import func
from typing import List
from sqlalchemy.orm import joinedload

def get_test_procedures(db: Session, skip: int = 0, limit: int = 10):
    result =  db.query(models.TestProcedure).offset(skip).limit(limit).all()
    return result

def create_test_procedure(db: Session, test_procedure: schemas.TestProcedureCreate):
    try:
        db_test_procedure = models.TestProcedure(**test_procedure.model_dump(exclude={"tag_ids"}))

        if test_procedure.tag_ids:
            tags = db.query(models.TestProcedureTagLookup).filter(models.TestProcedureTagLookup.id.in_(test_procedure.tag_ids)).all()
            if len(tags) != len(test_procedure.tag_ids):
                raise NoResultFound("One or more tags not found")
            db_test_procedure.tags = tags

        db.add(db_test_procedure)
        db.commit()
        db.refresh(db_test_procedure)
        return db_test_procedure
    except ValueError as e:
        db.rollback()
        raise HTTPException(status_code=400, detail=str(e))

def update_test_procedure(db: Session, test_procedure_id: int, test_procedure: schemas.TestProcedureUpdate):
    try:
        # Retrieve the current entry
        existing_entry = db.query(models.TestProcedure).filter(models.TestProcedure.id == test_procedure_id).one_or_none()

        if not existing_entry:
            raise NoResultFound(f"TestProcedure with id {test_procedure_id} not found")

        # Check if the version number is different
        if existing_entry.version != test_procedure.version:
            # Mark the existing entry as obsolete
            existing_entry.obsolete = True
            existing_entry.modified = datetime.now(datetime.timezone.utc)

            # Create a new entry
            new_entry = create_test_procedure(db, test_procedure)
            db.commit()
            return new_entry
        else:
            # Update the existing entry
            update_data = test_procedure.model_dump(exclude_unset=True, exclude={"tag_ids"})
            for key, value in update_data.items():
                setattr(existing_entry, key, value)
            tags = db.query(models.TestProcedureTagLookup).filter(models.TestProcedureTagLookup.id.in_(test_procedure.tag_ids)).all()
            if len(tags) != len(test_procedure.tag_ids):
                raise NoResultFound("One or more tags not found")
            existing_entry.tags = tags
            existing_entry.modified = datetime.utcnow()
            db.commit()
            db.refresh(existing_entry)
            return existing_entry
    except ValueError as e:
        db.rollback()
        raise HTTPException(status_code=400, detail=str(e))
    
def get_latest_test_date_per_procedure(db: Session):
    """
    Get the latest test creation date for each test procedure.

    :param db: SQLAlchemy Session object
    :return: List of dictionaries with test procedure ID and latest test creation date
    """
    result = (
        db.query(
            models.TestProcedure.id.label("test_procedure_id"),
            func.max(models.Test.date).label("latest_test_date")
        )
        .outerjoin(models.Test, models.Test.test_procedure_id == models.TestProcedure.id)
        .group_by(models.TestProcedure.id)
        .all()
    )

    return [schemas.TestDate(test_procedure_id=row.test_procedure_id, latest_test_date=row.latest_test_date) for row in result]

def delete_test_procedure(db: Session, test_procedure_external_id: int):
    try:
        db.query(models.TestProcedure).filter(models.TestProcedure.external_id == test_procedure_external_id).delete()
        db.commit()
        return {"message": "Test Procedure deleted successfully"}
    except IntegrityError:
        return {"message": "Test procedure cannot be deleted as it is being referenced by a test"}, 400
    
def delete_last_test_procedure_version(db: Session, test_procedure_id: int):
    try:
        # Retrieve the last version of the test procedure
        last_version = db.query(models.TestProcedure).filter(models.TestProcedure.id == test_procedure_id).first()
        if last_version:
            version = last_version.version
            external_id = last_version.external_id
            db.delete(last_version)
            second_last_version = db.query(models.TestProcedure).filter(models.TestProcedure.external_id == external_id, models.TestProcedure.version == version - 1).first()
            second_last_version.obsolete = False
            db.commit()
            return {"message": "Last version of the test procedure deleted successfully"}
        else:
            return {"message": "Test procedure not found"}, 404
    except IntegrityError:
        return {"message": "Test procedure cannot be deleted as it is being referenced by a test"}, 400

def get_tests(db: Session, skip: int = 0, limit: int = 10, test_procedure_id: int = None):
    query = db.query(models.Test)
    
    if test_procedure_id:
        query = query.filter(models.Test.test_procedure_id == test_procedure_id)
    
    return query.offset(skip).limit(limit).all()

def create_test(db: Session, test: schemas.TestCreate):
    db_test = models.Test(**test.dict())
    if test.automated:
        db_test.automated_execution_status = "PENDING"
        test_procedure = db.query(models.TestProcedure).filter(models.TestProcedure.id == db_test.test_procedure_id).first()
        db_test.celery_task_id = start_sim_test.delay(test_procedure.external_id).id
        logging.info(f"Starting test (Celery Task) {test_procedure.external_id}")
    db.add(db_test)
    db.commit()
    db.refresh(db_test)
    return db_test

def fetch_and_update_task_status(db: Session, task_id: str):
    result = celery_app.AsyncResult(task_id)
    status = result.status
    task_result = result.result if result.ready() else None

    db_test = db.query(models.Test).filter(models.Test.celery_task_id == task_id).first()
    if status != db_test.automated_execution_status:
        db_test.automated_execution_status = status
        if task_result:
            db_test.flight_review_log_url = task_result.get('flight_review_log_url')
        db.commit()
        db.refresh(db_test)
    return status, task_result

def update_task(db: Session, task_id: str, update: schemas.TaskUpdate):
    db_test = db.query(models.Test).filter(models.Test.celery_task_id == task_id).first()
    for key, value in update.dict().items():
        if value is not None:
            setattr(db_test, key, value)
    db.commit()
    db.refresh(db_test)

    return db_test

def update_test_report(db: Session, test_id: int, update: schemas.TestUpdate):
    db_test = db.query(models.Test).filter(models.Test.id == test_id).first()
    for key, value in update.dict().items():
        if value is not None:
            setattr(db_test, key, value)
    db.commit()
    db.refresh(db_test)

    return db_test

# Add new group
def add_group(db: Session, group_name: str):
    db_group = models.Group(name=group_name)
    db.add(db_group)
    db.commit()
    db.refresh(db_group)
    return db_group

def add_tag(db: Session, tag: schemas.TagCreate):
    db_tag = models.TestProcedureTagLookup(**tag.dict())
    db.add(db_tag)
    db.commit()
    db.refresh(db_tag)
    return db_tag

def get_tags(db: Session) -> List[schemas.Tag]:
    return db.query(models.TestProcedureTagLookup).all()

# Get all groups
def get_groups(db: Session):
    return db.query(models.Group).all()

# Get all types (from enum)
def get_test_types():
    return [test_type.value for test_type in TestTypeEnum]

# Get all environments (from enum)
def get_environments():
    return [environment.value for environment in EnvironmentEnum]

# Get all sim worlds (from enum)
def get_sim_worlds():
    return [sim_world.value for sim_world in SimWorldEnum]

# Get all wind modes (from enum)
def get_wind_modes():
    return [wind_mode.value for wind_mode in WindModeEnum]

# Get all autopilot modes (from enum)
def get_autopilot_modes():
    return [autopilot_mode.value for autopilot_mode in AutopilotModeEnum]

# Get all vtol modes (from enum)
def get_vtol_modes():
    return [vtol_mode.value for vtol_mode in VtolModeEnum]

# Get all priority levels (from enum)
def get_priority_levels():
    return [priority_level.value for priority_level in PriorityLevelEnum]


# Fetch all gliders in a single request from the glider microservice
async def fetch_all_gliders(user_token: str) -> dict:
    async with httpx.AsyncClient() as client:
        response = await client.get(f"{settings.microservices.glider}/gliders", headers={"Authorization": f"Bearer {user_token}"})
        if response.status_code == 200:
            gliders = response.json()
            # Convert the list to a dictionary {id: name}
            return {str(glider['id']): glider['name'] for glider in gliders}
        return {}

# Fetch all users (testers) from Keycloak in a single request
async def fetch_all_testers(user_token: str) -> dict:
    async with httpx.AsyncClient() as client:
        response = await client.get(f"{settings.keycloak.url}/admin/realms/jedsy/users", headers={"Authorization": f"Bearer {user_token}"})
        if response.status_code == 200:
            users = response.json()
            # Convert the list to a dictionary {id: username}
            return {user['id']: user['username'] for user in users}
        return {}

# Query the tests joined with test procedures
def query_tests_with_procedures(db: Session):
    return db.query(
        models.Test.id,
        models.TestProcedure.external_id.label('proc_external_id'),
        models.TestProcedure.name,
        models.Test.date,
        models.Test.tester_id,
        models.Test.glider,
        models.Test.flight_review_log_url,
        models.Test.success_status,
        models.Test.notes,
        models.TestProcedure.test_type,
        models.Group.name.label('group_name'),
        models.TestProcedure.priority_level,
        models.TestProcedure.qgroundcontrol,
        models.TestProcedure.precland_docker,
        models.TestProcedure.autopilot_mode,
        models.TestProcedure.vtol_mode,
        models.TestProcedure.mission_plan,
        models.TestProcedure.wind_mode,
        models.TestProcedure.wind_speed,
        models.TestProcedure.procedures_simulation,
        models.TestProcedure.expectations_simulation,
        models.TestProcedure.procedures_field,
        models.TestProcedure.expectations_field,
        models.TestProcedure.procedures_hil,
        models.TestProcedure.expectations_hil,
        models.TestProcedure.version,
        models.TestProcedure.obsolete,
        # SW component version fields
        models.Test.r_autopilot_version,
        models.Test.r_autopilot_rc_number,
        models.Test.glider_companion_version,
        models.Test.glider_companion_rc_number,
        models.Test.qgroundcontrol_version,
        models.Test.qgroundcontrol_rc_number,
        models.Test.reference_parameters_version,
        models.Test.reference_parameters_rc_number,
        models.Test.force_sensor_version,
        models.Test.force_sensor_rc_number,
        models.Test.landing_station_mercury_version,
        models.Test.landing_station_mercury_rc_number,
        models.Test.r_autopilot_fts_version,
        models.Test.r_autopilot_fts_rc_number,
        models.Test.fts_comms_server_version,
        models.Test.fts_comms_server_rc_number,
        models.Test.fts_trigger_android_app_version,
        models.Test.fts_trigger_android_app_rc_number
    ).join(models.TestProcedure, models.Test.test_procedure_id == models.TestProcedure.id).join(models.Group, models.TestProcedure.group_id == models.Group.id).all()


# Format arrays as hyphen lists
def format_array_as_hyphen_list(array):
    return '- ' + '\n- '.join(array or [])

# Generate CSV data from the query results
async def generate_csv(db: Session, user_token: str) -> StringIO:
    # Fetch external data for gliders and testers
    gliders = await fetch_all_gliders(user_token)
    testers = await fetch_all_testers(user_token)

    # Query to join tests and test_procedures tables
    tests_with_procedures = query_tests_with_procedures(db)

    # Use StringIO to create a CSV in-memory
    output = StringIO()
    writer = csv.writer(output)

    # Write CSV header
    writer.writerow([
        'Test UID', 'Procedure ID','Procedure Name', 'Date', 'Tester', 'Glider',
        'Review Link', 'Success Status', 'Notes', 'Test Type', 'Test Group',
        'Priority Level', 'QGroundControl', 'Precland Docker', 'Autopilot Mode',
        'Flight Mode', 'Mission Plan', 'Wind Mode', 'Wind Speed',
        'Procedures Simulation', 'Expectations Simulation', 'Procedures Field',
        'Expectations Field', 'Procedures Hil', 'Expectations Hil', 'Procedure Version', 'Procedure Version Is Active',
        # SW component version headers
        'R-Autopilot Version', 'R-Autopilot RC Number',
        'Glider-Companion Version', 'Glider-Companion RC Number',
        'QGroundControl Version', 'QGroundControl RC Number',
        'Reference-Parameters Version', 'Reference-Parameters RC Number',
        'Force-Sensor Version', 'Force-Sensor RC Number',
        'Landing-Station-Mercury Version', 'Landing-Station-Mercury RC Number',
        'R-Autopilot (FTS) Version', 'R-Autopilot (FTS) RC Number',
        'FTS-Comms-Server Version', 'FTS-Comms-Server RC Number',
        'FTS-Trigger-Android-App Version', 'FTS-Trigger-Android-App RC Number'
    ])

    # Write CSV rows with array formatting and external data fetching
    for test in tests_with_procedures:
        # Use fetched glider and tester data
        glider_name = gliders.get(test.glider, 'Unknown Glider')
        tester_name = testers.get(test.tester_id, 'Unknown Tester')

        # Format array columns as hyphen lists
        procedures_simulation = format_array_as_hyphen_list(test.procedures_simulation)
        expectations_simulation = format_array_as_hyphen_list(test.expectations_simulation)
        procedures_field = format_array_as_hyphen_list(test.procedures_field)
        expectations_field = format_array_as_hyphen_list(test.expectations_field)
        procedures_hil = format_array_as_hyphen_list(test.procedures_hil)
        expectations_hil = format_array_as_hyphen_list(test.expectations_hil)
        procedure_version = test.version
        procedure_is_active = "True" if not test.obsolete else "False"

        writer.writerow([
            test.id, test.proc_external_id, test.name, test.date, tester_name, glider_name,
            test.flight_review_log_url,
            "Pass" if test.success_status else "Fail",
            test.notes, test.test_type.value, test.group_name,
            test.priority_level.value, test.qgroundcontrol, test.precland_docker, test.autopilot_mode.value,
            test.vtol_mode.value, test.mission_plan, test.wind_mode.value, test.wind_speed,
            procedures_simulation, expectations_simulation, procedures_field,
            expectations_field, procedures_hil, expectations_hil, procedure_version, procedure_is_active,
            # SW component version data
            test.r_autopilot_version, test.r_autopilot_rc_number,
            test.glider_companion_version, test.glider_companion_rc_number,
            test.qgroundcontrol_version, test.qgroundcontrol_rc_number,
            test.reference_parameters_version, test.reference_parameters_rc_number,
            test.force_sensor_version, test.force_sensor_rc_number,
            test.landing_station_mercury_version, test.landing_station_mercury_rc_number,
            test.r_autopilot_fts_version, test.r_autopilot_fts_rc_number,
            test.fts_comms_server_version, test.fts_comms_server_rc_number,
            test.fts_trigger_android_app_version, test.fts_trigger_android_app_rc_number
        ])

    # Seek to the beginning of the stream
    output.seek(0)

    return output
