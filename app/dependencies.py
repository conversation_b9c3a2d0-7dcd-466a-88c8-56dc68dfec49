from fastapi import Depends, HTTPException
from sqlalchemy.orm import Session
from fastapi.security import OAuth2AuthorizationCodeBearer
from keycloak import KeycloakOpenID
from app.database import SessionLocal
from app.config import settings
import traceback

keycloak_openid = KeycloakOpenID(
    server_url=settings.keycloak.url,
    client_id=settings.keycloak.client_id,
    realm_name=settings.keycloak.realm,
    client_secret_key=settings.keycloak.client_secret
)

oauth2_scheme = OAuth2AuthorizationCodeBearer(
    authorizationUrl=f"{settings.keycloak.url}/realms/{settings.keycloak.realm}/protocol/openid-connect/auth",
    tokenUrl=f"{settings.keycloak.url}/realms/{settings.keycloak.realm}/protocol/openid-connect/token"
)

def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

def get_current_user(token: str = Depends(oauth2_scheme)):
    try:
        user_info = keycloak_openid.userinfo(token)

        if 'token' not in user_info:
            user_info['token'] = token
    except Exception as e:
        print(e)
        traceback.print_exc()
        raise HTTPException(status_code=401, detail="Invalid token")
    return user_info
