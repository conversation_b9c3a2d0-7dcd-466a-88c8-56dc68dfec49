from fastapi import <PERSON><PERSON><PERSON>, Depends, HTTPException
from sqlalchemy.orm import Session
from app import models, schemas, crud
from app.database import engine, SessionLocal
from app.dependencies import get_db, get_current_user
from typing import List
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import StreamingResponse
from app.celery import app as celery_app


models.Base.metadata.create_all(bind=engine)

app = FastAPI(
    title="Jedsy Flight Tests",
    description="An API for all the flight tests (automated, SIM, IRL, test procedures, ...)",
    version="0.0.1",
    docs_url="/swagger-ui",
    redoc_url="/docs",
    openapi_url="/openapi.json"
)

origins = [
    "*"
]

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,           # Allow specific origins
    allow_credentials=["*"],          # Allow cookies to be sent across origins
    allow_methods=["*"],             # Allow all HTTP methods (GET, POST, PUT, DELETE, etc.)
    allow_headers=["*"],             # Allow all headers
)

# @app.options("/{any_path:path}")
# async def options_handler(any_path: str):
#     return

@app.get("/active-workers/")
async def active_workers():
    # Inspect the Celery worker
    i = celery_app.control.inspect()
    active_workers = i.active()  # This queries for active workers
    
    # TODO: Use VHOST on rabbitmq to filter out workers
    if not active_workers or "celery@simulatorstation-Z790-UD" not in active_workers:
        return {"status": "No workers active", "active_workers": []}
    
    return {"status": "Worker(s) active", "active_workers": [active_workers['celery@simulatorstation-Z790-UD']]}


@app.post("/test-procedures/", response_model=schemas.TestProcedure)
def create_test_procedure(test_procedure: schemas.TestProcedureCreate, db: Session = Depends(get_db)):
    try:
        return crud.create_test_procedure(db=db, test_procedure=test_procedure)
    except HTTPException:
        raise  # Re-raise HTTPException as-is
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Failed to create test procedure: {str(e)}")

@app.delete("/test-procedures/{test_procedure_id}/")
def delete_test_procedure(test_procedure_external_id: int, db: Session = Depends(get_db)):
    return crud.delete_test_procedure(db=db, test_procedure_external_id=test_procedure_external_id)

@app.delete("/test-procedures/version/{test_procedure_id}/")
def delete_test_procedure_version(test_procedure_id: int, db: Session = Depends(get_db)):
    return crud.delete_last_test_procedure_version(db=db, test_procedure_id=test_procedure_id)

@app.patch("/test-procedures/{test_procedure_id}/", response_model=schemas.TestProcedure)
def update_test_procedure(test_procedure_id: int, test_procedure: schemas.TestProcedureUpdate, db: Session = Depends(get_db)):
    try:
        return crud.update_test_procedure(db=db, test_procedure_id=test_procedure_id, test_procedure=test_procedure)
    except HTTPException:
        raise  # Re-raise HTTPException as-is
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Failed to update test procedure: {str(e)}")

@app.get("/test-procedures/", response_model=List[schemas.TestProcedure])
def read_test_procedures(skip: int = 0, limit: int = 10, is_default: bool = None, db: Session = Depends(get_db)):
    """Get test procedures with optional filtering by is_default status."""
    test_procedures = crud.get_test_procedures(db, skip=skip, limit=limit, is_default=is_default)
    return test_procedures

@app.get("/test-procedures/default/", response_model=List[schemas.TestProcedure])
def read_default_test_procedures(skip: int = 0, limit: int = 10, db: Session = Depends(get_db)):
    """Get test procedures marked as default."""
    test_procedures = crud.get_default_test_procedures(db, skip=skip, limit=limit)
    return test_procedures

@app.patch("/test-procedures/{test_procedure_id}/set-default/", response_model=schemas.TestProcedure)
def set_test_procedure_as_default(test_procedure_id: int, db: Session = Depends(get_db)):
    """Set a test procedure as default."""
    try:
        return crud.set_default_test_procedure(db=db, test_procedure_id=test_procedure_id)
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Failed to set test procedure as default: {str(e)}")

@app.patch("/test-procedures/{test_procedure_id}/unset-default/", response_model=schemas.TestProcedure)
def unset_test_procedure_as_default(test_procedure_id: int, db: Session = Depends(get_db)):
    """Unset a test procedure as default."""
    try:
        return crud.unset_default_test_procedure(db=db, test_procedure_id=test_procedure_id)
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Failed to unset test procedure as default: {str(e)}")

@app.get("/test-procedures/latest-test-dates/", response_model=List[schemas.TestDate])
def read_latest_tests(db: Session = Depends(get_db)):
    tests = crud.get_latest_test_date_per_procedure(db)
    return tests

@app.post("/tests/", response_model=schemas.Test)
def create_test(test: schemas.TestCreate, db: Session = Depends(get_db)):
    result = crud.create_test(db=db, test=test)
    return result

@app.patch("/tests/{test_id}", response_model=schemas.Test)
def update_test(test: schemas.TestUpdate, test_id: int, db: Session = Depends(get_db)):
    return crud.update_test_report(db=db, test_id=test_id, update=test)

@app.patch("/tasks/{task_id}", response_model=schemas.Test)
def update_task_status(update: schemas.TaskUpdate, task_id: str, db: Session = Depends(get_db)):
    return crud.update_task(db=db, task_id=task_id, update=update)

@app.get("/tests/", response_model=List[schemas.Test])
def read_tests(skip: int = 0, limit: int = 10, test_procedure_id: int = None, db: Session = Depends(get_db)):
    tests = crud.get_tests(db, skip=skip, limit=limit, test_procedure_id=test_procedure_id)
    return tests

@app.post("/groups/", response_model=schemas.Group)
def create_group(group: schemas.GroupCreate, db: Session = Depends(get_db)):
    return crud.add_group(db=db, group_name=group.name)

@app.get("/groups/", response_model=List[schemas.Group])
def get_groups(db: Session = Depends(get_db)):
    return crud.get_groups(db=db)

@app.get("/test-types/", response_model=List[str])
def get_test_types():
    return crud.get_test_types()

@app.get("/environments/", response_model=List[str])
def get_environments():
    return crud.get_environments()

@app.get("/sim-worlds/", response_model=List[str])
def get_sim_worlds():
    return crud.get_sim_worlds()

# Endpoint to get all values for WindModeEnum
@app.get("/wind-modes/", response_model=List[str])
def get_wind_modes():
    return crud.get_wind_modes()

# Endpoint to get all values for AutopilotModeEnum
@app.get("/autopilot-modes/", response_model=List[str])
def get_autopilot_modes():
    return crud.get_autopilot_modes()

# Endpoint to get all values for VtolModeEnum
@app.get("/vtol-modes/", response_model=List[str])
def get_vtol_modes():
    return crud.get_vtol_modes()

@app.get("/enums/priority-levels/", response_model=List[str])
def get_priority_levels():
    return crud.get_priority_levels()

@app.post("/tags", response_model=schemas.TagBase)
def create_tag(tag: schemas.TagCreate, db: Session = Depends(get_db)):
    return crud.add_tag(db=db, tag=tag)

@app.get("/tags", response_model=List[schemas.Tag])
def get_tags(db: Session = Depends(get_db)):
    return crud.get_tags(db=db)

@app.get("/export-tests/", response_class=StreamingResponse, responses={
    200: {
        "content": {"text/csv": {}},
        "description": "CSV file containing the export of tests and procedures data."
    }
}, response_description="Returns a CSV file of tests joined with procedures.")
async def export_tests_to_csv(db: Session = Depends(get_db), user_info: dict = Depends(get_current_user)):
    output = await crud.generate_csv(db, user_info['token'])
    return StreamingResponse(output, media_type="text/csv", headers={
        "Content-Disposition": "attachment; filename=test_results.csv"
    })