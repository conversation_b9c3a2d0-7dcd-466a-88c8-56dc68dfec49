from sqlalchemy import <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, DateTime, Enum, Text
from sqlalchemy.orm import relationship, validates
from sqlalchemy.sql import func
from sqlalchemy.dialects.postgresql import UUID, ARRAY
import enum

from sqlalchemy import event
from sqlalchemy.orm import Session

from sqlalchemy.ext.declarative import declarative_base
Base = declarative_base()

class TestTypeEnum(enum.Enum):
    DEFAULT = "Default"
    NORMAL = "Normal"
    CONTINGENCY = "Contingency"
    EMERGENCY = "Emergency"

class EnvironmentEnum(enum.Enum):
    SIM = 'SIM'
    HIL = 'HIL'
    IRL = 'IRL'

class SimWorldEnum(enum.Enum):
    WINDY = "windy"
    CANYON = "canyon"
    PIVOT = "pivot"

class AutopilotModeEnum(enum.Enum):
    MANUAL = "Manual"
    STABILIZED = "Stabilized"
    ACRO = "Acro"
    ALTITUDE = "Altitude"
    POSITION = "Position"
    HOLD = "Hold"
    MISSION = "Mission"
    RALLY_POINT_LAND = "Rally Point Land"
    LAND = "Land"
    PRECISION_LAND = "Precision Land"
    TAKEOFF = "Takeoff"
    DISARMED = "Disarmed"
    ARMED = "Armed"

# Wind Mode Enum
class WindModeEnum(enum.Enum):
    NONE = "None"
    SIDE_WIND = "Side Wind"
    TAIL_WIND = "Tail Wind"
    HEAD_WIND = "Head Wind"
    VERTICAL_WIND = "Vertical Wind"
    ENVIRONMENT = "Environment"
    ARBITRARY = "Arbitrary"

# VTOL Mode Enum
class VtolModeEnum(enum.Enum):
    MC_MODE = "MC Mode"
    DURING_FRONT_TRANSITION = "During Front Transition"
    FW_MODE = "FW Mode"
    DURING_BACK_TRANSITION = "During Back Transition"
    MISSION_INHERITED = "Mission Inherited"
    ON_GROUND = "On Ground"

class PriorityLevelEnum(enum.Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"

class TestProcedure(Base):
    __tablename__ = "test_procedures"

    id = Column(Integer, primary_key=True, index=True)
    external_id = Column(Integer, nullable=False, index=False, unique=False)
    name = Column(String(100), index=True)
    version = Column(Integer, nullable=False, server_default="1")
    obsolete = Column(Boolean, server_default="false")
    
    test_type = Column(Enum(TestTypeEnum), nullable=False)
    
    group_id = Column(Integer, ForeignKey("group.id"), nullable=False)
    priority_level = Column(Enum(PriorityLevelEnum), nullable=True)

    simulation = Column(Boolean, default=True)
    field_test = Column(Boolean, default=False)
    hil_test = Column(Boolean, default=False)
    automated = Column(Boolean, default=False)
    qgroundcontrol = Column(Boolean, default=True)
    precland_docker = Column(Boolean, default=False)
    mdp = Column(Boolean, default=False)
    
    mission_plan = Column(String(60), nullable=True, default=None)
    wind_speed = Column(Integer, default=0)

    sim_world = Column(Enum(SimWorldEnum), nullable=True)
    wind_mode = Column(Enum(WindModeEnum), default=WindModeEnum.NONE)
    autopilot_mode = Column(Enum(AutopilotModeEnum), default=AutopilotModeEnum.MANUAL)
    vtol_mode = Column(Enum(VtolModeEnum), default=VtolModeEnum.MISSION_INHERITED)
    
    procedures_simulation = Column(ARRAY(Text), default=[])
    expectations_simulation = Column(ARRAY(Text), default=[])
    procedures_field = Column(ARRAY(Text), default=[])
    expectations_field = Column(ARRAY(Text), default=[])
    procedures_hil = Column(ARRAY(Text), default=[])
    expectations_hil = Column(ARRAY(Text), default=[])

    tests = relationship("Test", back_populates="test_procedure")
    group = relationship("Group", back_populates="test_procedures")

    tags = relationship("TestProcedureTagLookup", back_populates="test_procedures", secondary="test_procedure_tag_map")

    def save(self, *args, **kwargs):
        self.name = self.name.upper()
        super().save(*args, **kwargs)

# Event listener to automatically set `external_id`
@event.listens_for(TestProcedure, "before_insert")
def set_external_id(mapper, connection, target):
    if target.external_id is not None:
        return
    # Query to get the maximum `external_id` value
    session = Session.object_session(target)
    max_external_id = session.query(func.max(TestProcedure.external_id)).scalar()
    
    # If there are no previous records, start with 1
    if max_external_id is None:
        max_external_id = 0
    # Set external_id to the max + 1
    target.external_id = max_external_id + 1

class Group(Base):
    __tablename__ = "group"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), unique=True, nullable=False)

    test_procedures = relationship("TestProcedure", back_populates="group")

class Test(Base):
    __tablename__ = "tests"

    id = Column(Integer, primary_key=True, index=True)
    test_procedure_id = Column(Integer, ForeignKey("test_procedures.id"))
    date = Column(DateTime(timezone=True), server_default=func.now())
    tester_id = Column(UUID(as_uuid=False), nullable=False)
    
    # Simulation field
    simulation = Column(Boolean, default=True)
    
    # Software version fields - R-Autopilot (previously autopilot_software_version)
    r_autopilot_version = Column(String(100), nullable=True, default="not_tracked")
    r_autopilot_rc_number = Column(Integer, nullable=True, default=0)

    # Software version fields - glider-companion (previously jetson_software_version)
    glider_companion_version = Column(String(100), nullable=True, default="not_tracked")
    glider_companion_rc_number = Column(Integer, nullable=True, default=0)

    # Software version fields - qgroundcontrol
    qgroundcontrol_version = Column(String(100), nullable=True, default="not_tracked")
    qgroundcontrol_rc_number = Column(Integer, nullable=True, default=0)

    # Software version fields - reference-parameters
    reference_parameters_version = Column(String(100), nullable=True, default="not_tracked")
    reference_parameters_rc_number = Column(Integer, nullable=True, default=0)

    # Software version fields - force-sensor
    force_sensor_version = Column(String(100), nullable=True, default="not_tracked")
    force_sensor_rc_number = Column(Integer, nullable=True, default=0)

    # Software version fields - landing-station-mercury
    landing_station_mercury_version = Column(String(100), nullable=True, default="not_tracked")
    landing_station_mercury_rc_number = Column(Integer, nullable=True, default=0)

    # Software version fields - R-Autopilot (FTS)
    r_autopilot_fts_version = Column(String(100), nullable=True, default="not_tracked")
    r_autopilot_fts_rc_number = Column(Integer, nullable=True, default=0)

    # Software version fields - fts-comms-server
    fts_comms_server_version = Column(String(100), nullable=True, default="not_tracked")
    fts_comms_server_rc_number = Column(Integer, nullable=True, default=0)

    # Software version fields - fts-trigger-android-app
    fts_trigger_android_app_version = Column(String(100), nullable=True, default="not_tracked")
    fts_trigger_android_app_rc_number = Column(Integer, nullable=True, default=0)
    
    # Glider cannot be null if simulation is False
    glider = Column(String(100), nullable=True)
    
    # Success status: can be null only under certain conditions
    success_status = Column(Boolean, nullable=True)
    
    notes = Column(Text)
    flight_review_log_url = Column(String(250))
    
    # Automated-related fields
    automated_execution_status = Column(String(100), default="PENDING", nullable=True)
    automated = Column(Boolean, default=False)
    celery_task_id = Column(String(100), nullable=True, default=None)

    test_procedure = relationship(TestProcedure, back_populates="tests")

    test_campaign_id = Column(Integer, nullable=True, default=0)
    manually_cleared = Column(Boolean, nullable=False, default=False)

    # Validation and constraints
    @validates('glider')
    def validate_glider(self, key, value):
        """Glider cannot be null if simulation is False."""
        if not self.simulation and not value:
            raise ValueError("Glider cannot be null when simulation is False.")
        return value

    # @validates('success_status')
    # def validate_success_status(self, key, value):
    #     """Success status can only be null if automated and execution status is not 'success'."""
    #     print(self.automated, self.automated_execution_status)
    #     if self.automated and self.automated_execution_status != "SUCCESS" and value is None:
    #         return value
    #     elif not self.automated and value is None:
    #         raise ValueError("Success status cannot be null if the test is not automated.")
    #     return value

    # @validates('celery_task_id', 'automated_execution_status')
    # def validate_automated_fields(self, key, value):
    #     """Celery task ID and automated execution status can only be null if the test is not automated."""
    #     if not self.automated and value is None:
    #         return value
    #     elif self.automated and value is None:
    #         raise ValueError(f"{key} cannot be null if the test is automated.")
    #     return value

    # @validates('automated')
    # def validate_automated(self, key, value):
    #     """Automated can only be true if the corresponding test procedure is automated."""
    #     if value and not self.test_procedure.automated:
    #         raise ValueError("Automated can only be true if the corresponding test procedure is automated.")
    #     return value

class TestProcedureTagLookup(Base):
    __tablename__ = "test_procedure_tag_lookup"

    id = Column(Integer, primary_key=True, index=True)
    display_name = Column(String(100), nullable=False)
    description = Column(String(250), nullable=True)
    is_default = Column(Boolean, default=False)

    test_procedures = relationship(TestProcedure, back_populates="tags", secondary="test_procedure_tag_map")

class TestProcedureTagMap(Base):
    __tablename__ = "test_procedure_tag_map"

    id = Column(Integer, primary_key=True, index=True)
    test_procedure_id = Column(Integer, ForeignKey("test_procedures.id"))
    tag_id = Column(Integer, ForeignKey("test_procedure_tag_lookup.id"))
