from pydantic import BaseModel, ConfigDict, field_validator
from typing import List, Optional
from datetime import datetime
from app.models import PriorityLevelEnum, SimWorldEnum, AutopilotModeEnum, VtolModeEnum, WindModeEnum

class TestDate(BaseModel):
    test_procedure_id: int
    latest_test_date: Optional[datetime] = None

class GroupBase(BaseModel):
    name: str

class GroupCreate(GroupBase):
    pass

class Group(GroupBase):
    id: int

    model_config = ConfigDict(from_attributes=True)

class TagBase(BaseModel):
    display_name: str
    description: str
    is_default: bool

    model_config = ConfigDict(from_attributes=True)

class TagCreate(TagBase):
    pass

class Tag(TagBase):
    id: int

class TestProcedureBase(BaseModel):
    name: str
    version: int
    obsolete: bool = False
    external_id: Optional[int] = None
    test_type: str
    group_id: int

    # Updated to use PriorityLevelEnum
    priority_level: PriorityLevelEnum

    simulation: bool
    field_test: bool
    hil_test: bool
    automated: bool
    qgroundcontrol: bool
    precland_docker: bool
    mdp: bool

    # sim_world is now nullable and uses the SimWorldEnum
    sim_world: Optional[SimWorldEnum] = None
    
    # Updated to use enums for autopilot_mode, vtol_mode, and wind_mode
    autopilot_mode: AutopilotModeEnum
    vtol_mode: VtolModeEnum  # Renamed from flight_mode

    mission_plan: Optional[str] = None
    wind_mode: WindModeEnum
    wind_speed: int

    is_default: Optional[bool] = False

    # Array fields
    procedures_simulation: List[str] = []
    expectations_simulation: List[str] = []
    procedures_field: List[str] = []
    expectations_field: List[str] = []
    procedures_hil: List[str] = []
    expectations_hil: List[str] = []

    model_config = ConfigDict(from_attributes=True)

    @field_validator('simulation', 'field_test', 'hil_test')
    @classmethod
    def validate_test_environment_exclusivity(cls, v, info):
        """Ensure at most one of simulation, field_test, hil_test can be True."""
        if v is True and info.data:
            # Count how many of the three fields are True
            test_env_fields = ['simulation', 'field_test', 'hil_test']
            true_count = sum(1 for field in test_env_fields
                           if info.data.get(field, False) is True)

            # If this field is being set to True and others are already True
            if true_count > 0:
                true_fields = [field for field in test_env_fields
                             if info.data.get(field, False) is True]
                raise ValueError(
                    f"At most one of simulation, field_test, hil_test can be True. "
                    f"Currently attempting to set {info.field_name}=True, but these fields are already True: {true_fields}"
                )

        return v

class TestProcedureCreate(TestProcedureBase):
    tag_ids: List[int] = []

class TestProcedureUpdate(TestProcedureBase):
    tag_ids: List[int] = []

class TestProcedure(TestProcedureBase):
    id: int
    group: Group
    tags: List[TagBase]

    class Config:
        from_attributes = True

class TestBase(BaseModel):
    test_procedure_id: int
    simulation: bool
    glider: Optional[str] = None
    success_status: Optional[bool] = None
    notes: str
    flight_review_log_url: str
    automated_execution_status: Optional[str] = None
    celery_task_id: Optional[str] = None
    automated: bool
    tester_id: str
    date: Optional[datetime] = None
    # R-Autopilot SW component
    r_autopilot_version: Optional[str] = "not_tracked"
    r_autopilot_rc_number: Optional[int] = 0

    # glider-companion SW component
    glider_companion_version: Optional[str] = "not_tracked"
    glider_companion_rc_number: Optional[int] = 0

    # qgroundcontrol SW component
    qgroundcontrol_version: Optional[str] = "not_tracked"
    qgroundcontrol_rc_number: Optional[int] = 0

    # reference-parameters SW component
    reference_parameters_version: Optional[str] = "not_tracked"
    reference_parameters_rc_number: Optional[int] = 0

    # force-sensor SW component
    force_sensor_version: Optional[str] = "not_tracked"
    force_sensor_rc_number: Optional[int] = 0

    # landing-station-mercury SW component
    landing_station_mercury_version: Optional[str] = "not_tracked"
    landing_station_mercury_rc_number: Optional[int] = 0

    # R-Autopilot (FTS) SW component
    r_autopilot_fts_version: Optional[str] = "not_tracked"
    r_autopilot_fts_rc_number: Optional[int] = 0

    # fts-comms-server SW component
    fts_comms_server_version: Optional[str] = "not_tracked"
    fts_comms_server_rc_number: Optional[int] = 0

    # fts-trigger-android-app SW component
    fts_trigger_android_app_version: Optional[str] = "not_tracked"
    fts_trigger_android_app_rc_number: Optional[int] = 0
    test_campaign_id: Optional[int] = 0
    manually_cleared: Optional[bool] = False # If true, this test run should be ignored from both the statistics and the exported table


class TestCreate(TestBase):
    pass

class Test(TestBase):
    id: int

    class Config:
        from_attributes = True

class TestUpdate(BaseModel):
    simulation: bool
    glider: Optional[str] = None
    success_status: Optional[bool] = None
    notes: str
    flight_review_log_url: str
    date: Optional[datetime] = None
    # R-Autopilot SW component
    r_autopilot_version: Optional[str] = None
    r_autopilot_rc_number: Optional[int] = None

    # glider-companion SW component
    glider_companion_version: Optional[str] = None
    glider_companion_rc_number: Optional[int] = None

    # qgroundcontrol SW component
    qgroundcontrol_version: Optional[str] = None
    qgroundcontrol_rc_number: Optional[int] = None

    # reference-parameters SW component
    reference_parameters_version: Optional[str] = None
    reference_parameters_rc_number: Optional[int] = None

    # force-sensor SW component
    force_sensor_version: Optional[str] = None
    force_sensor_rc_number: Optional[int] = None

    # landing-station-mercury SW component
    landing_station_mercury_version: Optional[str] = None
    landing_station_mercury_rc_number: Optional[int] = None

    # R-Autopilot (FTS) SW component
    r_autopilot_fts_version: Optional[str] = None
    r_autopilot_fts_rc_number: Optional[int] = None

    # fts-comms-server SW component
    fts_comms_server_version: Optional[str] = None
    fts_comms_server_rc_number: Optional[int] = None

    # fts-trigger-android-app SW component
    fts_trigger_android_app_version: Optional[str] = None
    fts_trigger_android_app_rc_number: Optional[int] = None


class TaskUpdate(BaseModel):
    automated_execution_status: Optional[str] = None
    flight_review_log_url: Optional[str] = None

