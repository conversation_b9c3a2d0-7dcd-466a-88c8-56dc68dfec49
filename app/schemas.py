from pydantic import BaseModel, ConfigDict
from typing import List, Optional
from datetime import datetime
from app.models import PriorityLevelEnum, SimWorldEnum, AutopilotModeEnum, VtolModeEnum, WindModeEnum

class TestDate(BaseModel):
    test_procedure_id: int
    latest_test_date: Optional[datetime] = None

class GroupBase(BaseModel):
    name: str

class GroupCreate(GroupBase):
    pass

class Group(GroupBase):
    id: int

    model_config = ConfigDict(from_attributes=True)

class TagBase(BaseModel):
    display_name: str
    description: str
    is_default: bool

    model_config = ConfigDict(from_attributes=True)

class TagCreate(TagBase):
    pass

class Tag(TagBase):
    id: int

class TestProcedureBase(BaseModel):
    name: str
    version: int
    obsolete: bool = False
    external_id: Optional[int] = None
    test_type: str
    group_id: int

    # Updated to use PriorityLevelEnum
    priority_level: PriorityLevelEnum

    simulation: bool
    field_test: bool
    hil_test: bool
    automated: bool
    qgroundcontrol: bool
    precland_docker: bool
    mdp: bool

    # sim_world is now nullable and uses the SimWorldEnum
    sim_world: Optional[SimWorldEnum] = None
    
    # Updated to use enums for autopilot_mode, vtol_mode, and wind_mode
    autopilot_mode: AutopilotModeEnum
    vtol_mode: VtolModeEnum  # Renamed from flight_mode

    mission_plan: Optional[str] = None
    wind_mode: WindModeEnum
    wind_speed: int

    # Array fields
    procedures_simulation: List[str] = []
    expectations_simulation: List[str] = []
    procedures_field: List[str] = []
    expectations_field: List[str] = []
    procedures_hil: List[str] = []
    expectations_hil: List[str] = []

    model_config = ConfigDict(from_attributes=True)

class TestProcedureCreate(TestProcedureBase):
    tag_ids: List[int] = []

class TestProcedureUpdate(TestProcedureBase):
    tag_ids: List[int] = []

class TestProcedure(TestProcedureBase):
    id: int
    group: Group
    tags: List[TagBase]

    class Config:
        from_attributes = True

class TestBase(BaseModel):
    test_procedure_id: int
    simulation: bool
    glider: Optional[str] = None
    success_status: Optional[bool] = None
    notes: str
    flight_review_log_url: str
    automated_execution_status: Optional[str] = None
    celery_task_id: Optional[str] = None
    automated: bool
    tester_id: str
    date: Optional[datetime] = None
    autopilot_software_version: Optional[str] = None
    jetson_software_version: Optional[str] = None
    test_campaign_id: Optional[int] = 0
    manually_cleared: Optional[bool] = False # If true, this test run should be ignored from both the statistics and the exported table


class TestCreate(TestBase):
    pass

class Test(TestBase):
    id: int

    class Config:
        from_attributes = True

class TestUpdate(BaseModel):
    simulation: bool
    glider: Optional[str] = None
    success_status: Optional[bool] = None
    notes: str
    flight_review_log_url: str
    date: Optional[datetime] = None
    autopilot_software_version: Optional[str] = None
    jetson_software_version: Optional[str] = None


class TaskUpdate(BaseModel):
    automated_execution_status: Optional[str] = None
    flight_review_log_url: Optional[str] = None

