database:
  url: ${oc.env:POSTGRES_URL}

keycloak:
  url: ${oc.env:KEYCLOAK_URL}
  realm: ${oc.env:KEYCLOAK_REALM}
  client_id: ${oc.env:KEYCLOAK_CLIENT_ID}
  client_secret: ${oc.env:<PERSON><PERSON><PERSON><PERSON><PERSON>K_CLIENT_SECRET}

rabbitmq:
  host: ${oc.env:RABBITMQ_HOST}
  port: ${oc.env:RABBITMQ_PORT}
  username: ${oc.env:RABBITMQ_USER}
  password: ${oc.env:RABBITMQ_PASSWORD}

microservices:
  glider: ${oc.env:GLIDER_URL}
