version: '3.3'

services:
  db:
    image: postgres:13
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
      POSTGRES_DB: postgres
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - app-network

  keycloak:
    image: quay.io/keycloak/keycloak:latest
    environment:
      KEYCLOAK_USER: admin
      <PERSON>K_PASSWORD: admin
    ports:
      - "8080:8080"
    networks:
      - app-network

  app:
    build: .
    ports:
      - "8000:8000"
    depends_on:
      - db
      - keycloak
    networks:
      - app-network

networks:
  app-network:

volumes:
  postgres_data:
